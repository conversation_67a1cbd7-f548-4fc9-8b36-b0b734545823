import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Flex,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  hubspot
} from "@hubspot/ui-extensions";

// Define the extension to be run within the HubSpot CRM
hubspot.extend(({ context, actions }) => <RMSContactCard context={context} actions={actions} />);

const RMSContactCard = ({ context, actions }) => {
  const [cardData, setCardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Extract context properties
  const {
    user,
    portal,
    crm: { objectId, objectType }
  } = context;

  // Get contact properties
  const contactProperties = context.crm?.objectProperties || {};
  const firstname = contactProperties.firstname || '';
  const lastname = contactProperties.lastname || '';
  const email = contactProperties.email || '';
  const phone = contactProperties.phone || '';

  // Build API URL with dynamic parameters
  const buildApiUrl = () => {
    const baseUrl = 'https://api.niswey.net/rmscrmseries/api/hubspot/crm';
    const params = new URLSearchParams({
      userId: user?.id || '',
      userEmail: user?.email || '',
      associatedObjectId: objectId || '',
      associatedObjectType: objectType || 'CONTACT',
      portalId: portal?.id || '',
      firstname: firstname,
      phone: phone,
      email: email,
      lastname: lastname
    });

    return `${baseUrl}?${params.toString()}`;
  };

  // Fetch data from API
  const fetchCardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = buildApiUrl();
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setCardData(data);
    } catch (err) {
      console.error('Error fetching card data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchCardData();
  }, []);

  // Handle "See Mapping" button click
  const handleSeeMappingClick = () => {
    if (cardData?.results?.[0]?.actions?.[0]) {
      const action = cardData.results[0].actions[0];

      if (action.type === 'IFRAME') {
        // Open iframe modal using HubSpot actions
        actions.openIframeModal({
          uri: action.uri,
          height: action.height || 900,
          width: action.width || 1280
        });
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card>
        <Flex direction="column" align="center" gap="medium">
          <LoadingSpinner />
          <Text>Loading RMS data...</Text>
        </Flex>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <Alert title="Error" variant="error">
          Failed to load RMS data: {error}
        </Alert>
        <Button
          onClick={fetchCardData}
          variant="primary"
          size="small"
        >
          Retry
        </Button>
      </Card>
    );
  }

  // No data state
  if (!cardData?.results?.[0]) {
    return (
      <Card>
        <Alert title="No Data" variant="warning">
          No RMS data available for this contact.
        </Alert>
      </Card>
    );
  }

  const result = cardData.results[0];
  const hasAction = result.actions?.[0];

  return (
    <Card>
      <Flex direction="column" gap="medium">
        {/* Header with title and external link icon */}
        <Flex justify="between" align="center">
          <Flex align="center" gap="small">
            <Text format={{ fontWeight: "bold", fontSize: "large" }}>
              {result.title}
            </Text>
            <Text>🔗</Text>
          </Flex>

          {/* Actions dropdown */}
          {hasAction && (
            <Flex direction="column" align="end">
              <Button
                variant="secondary"
                size="small"
                onClick={handleSeeMappingClick}
              >
                Actions ▼
              </Button>
            </Flex>
          )}
        </Flex>

        {/* Action button */}
        {hasAction && (
          <Flex justify="end">
            <Button
              variant="primary"
              size="medium"
              onClick={handleSeeMappingClick}
            >
              {result.actions[0].label}
            </Button>
          </Flex>
        )}

        {/* Footer */}
        <Flex justify="start">
          <Text format={{ fontSize: "small", color: "secondary" }}>
            Powered by RMS APP
          </Text>
        </Flex>
      </Flex>
    </Card>
  );
};
