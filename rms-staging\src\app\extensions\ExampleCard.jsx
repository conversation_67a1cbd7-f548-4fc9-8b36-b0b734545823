import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Flex,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  hubspot,
  serverless
} from "@hubspot/ui-extensions";

// Define the extension to be run within the HubSpot CRM
hubspot.extend(({ context, actions }) =>
  <RMSContactCard context={context} actions={actions} />
);

const RMSContactCard = ({ context, actions }) => {
  const [cardData, setCardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Extract context properties
  const {
    user,
    portal,
    crm: { objectId, objectType }
  } = context;

  // Get contact properties
  const contactProperties = context.crm?.objectProperties || {};
  const firstname = contactProperties.firstname || '';
  const lastname = contactProperties.lastname || '';
  const email = contactProperties.email || '';
  const phone = contactProperties.phone || '';

  // Fetch data from serverless function or use mock data
  const fetchCardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare parameters for the serverless function
      const functionParams = {
        userId: user?.id || '',
        userEmail: user?.email || '',
        associatedObjectId: objectId || '',
        associatedObjectType: objectType || 'CONTACT',
        portalId: portal?.id || '',
        firstname: firstname,
        phone: phone,
        email: email,
        lastname: lastname
      };

      console.log('Calling serverless function with params:', functionParams);

      try {
        // Try to call the serverless function using HubSpot's new serverless API
        const response = await serverless.call({
          name: 'get-rms-data',
          parameters: functionParams
        });

        console.log('Serverless function response:', response);

        // Handle the new serverless API response format
        if (response?.body?.success) {
          setCardData(response.body.data);
        } else if (response?.success) {
          setCardData(response.data);
        } else {
          throw new Error(response?.body?.error || response?.error || 'Unknown error from serverless function');
        }
      } catch (serverlessError) {
        console.warn('Serverless function failed, using mock data:', serverlessError);

        // Use mock data that matches your API response format
        const mockData = {
          results: [
            {
              title: "Setting",
              objectId: "964252",
              link: "https://api.niswey.net/rmscrmseries/wizard?portal_id=" + (portal?.id || '242859663') + "&email=" + encodeURIComponent(user?.email || '<EMAIL>') + "&user_id=mock_user_id",
              actions: [
                {
                  type: "IFRAME",
                  width: 1280,
                  height: 900,
                  uri: "https://api.niswey.net/rmscrmseries/initiate?portal_id=" + (portal?.id || '242859663') + "&email=" + encodeURIComponent(user?.email || '<EMAIL>') + "&user_id=mock_user_id",
                  label: "See Mapping"
                }
              ]
            }
          ]
        };

        setCardData(mockData);
      }
    } catch (err) {
      console.error('Error fetching card data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchCardData();
  }, []);

  // Handle "See Mapping" button click
  const handleSeeMappingClick = () => {
    if (cardData?.results?.[0]?.actions?.[0]) {
      const action = cardData.results[0].actions[0];

      if (action.type === 'IFRAME') {
        // Open iframe modal using HubSpot actions
        actions.openIframeModal({
          uri: action.uri,
          height: action.height || 900,
          width: action.width || 1280
        });
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card>
        <Flex direction="column" align="center" gap="medium">
          <LoadingSpinner />
          <Text>Loading RMS data...</Text>
        </Flex>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <Alert title="Error" variant="error">
          Failed to load RMS data: {error}
        </Alert>
        <Button
          onClick={fetchCardData}
          variant="primary"
          size="small"
        >
          Retry
        </Button>
      </Card>
    );
  }

  // No data state
  if (!cardData?.results?.[0]) {
    return (
      <Card>
        <Alert title="No Data" variant="warning">
          No RMS data available for this contact.
        </Alert>
      </Card>
    );
  }

  const result = cardData.results[0];
  const hasAction = result.actions?.[0];

  return (
    <Card>
      <Flex direction="column" gap="medium">
        {/* Header with title and external link icon */}
        <Flex justify="between" align="center">
          <Flex align="center" gap="small">
            <Text format={{ fontWeight: "bold", fontSize: "large" }}>
              {result.title}
            </Text>
            <Text>🔗</Text>
          </Flex>

          {/* Actions dropdown */}
          {hasAction && (
            <Flex direction="column" align="end">
              <Button
                variant="secondary"
                size="small"
                onClick={handleSeeMappingClick}
              >
                Actions ▼
              </Button>
            </Flex>
          )}
        </Flex>

        {/* Action button */}
        {hasAction && (
          <Flex justify="end">
            <Button
              variant="primary"
              size="medium"
              onClick={handleSeeMappingClick}
            >
              {result.actions[0].label}
            </Button>
          </Flex>
        )}

        {/* Footer */}
        <Flex justify="start">
          <Text format={{ fontSize: "small", color: "secondary" }}>
            Powered by RMS APP
          </Text>
        </Flex>
      </Flex>
    </Card>
  );
};
