{"name": "RMS APP", "uid": "get-started-rms-app", "description": "An example to demonstrate how to build a public app with developer projects.", "allowedUrls": ["https://api.niswey.net/rmscrmseries/wizard", "https://api.niswey.net/rmscrmseries/initiate"], "auth": {"redirectUrls": ["https://api.niswey.net/rmscrmseries/auth"], "requiredScopes": ["crm.schemas.companies.write", "crm.schemas.contacts.write", "crm.schemas.deals.read", "crm.schemas.deals.write", "o<PERSON>h", "crm.objects.owners.read", "crm.objects.users.read", "crm.objects.contacts.write", "crm.objects.users.write", "crm.objects.companies.write", "crm.objects.companies.read", "crm.objects.deals.read", "crm.schemas.contacts.read", "crm.objects.deals.write", "crm.objects.contacts.read", "crm.schemas.companies.read"], "optionalScopes": [], "conditionallyRequiredScopes": []}, "support": {"supportEmail": "<EMAIL>", "documentationUrl": "https://example.com/docs", "supportUrl": "https://example.com/support", "supportPhone": "+18005555555"}, "extensions": {"crm": {"cards": [{"file": "./extensions/example-card.json"}]}}, "functions": {"file": "./functions"}, "webhooks": {"file": "./webhooks/webhooks.json"}}